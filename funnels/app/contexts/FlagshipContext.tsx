import { FlagshipProvider } from '@flagship.io/react-sdk';
import { useVisitorId } from 'hooks';
import { ReactNode, useState, useEffect } from 'react';
import { useAuth } from 'react-oidc-context';
import { IMemberDetails } from 'types';

import { GlobalLoader } from '~/components/ui/Loader';
import { useCurrentMember } from '~/hooks/reactQuery/user/useUser';
import { FLAGSHIP_API_KEY, FLAGSHIP_ENV_ID } from '~/utils/constants';

interface IFlagshipContextProps {
  children: ReactNode;
}

export const FlagshipContext = ({ children }: IFlagshipContextProps) => {
  const [isFlagshipSdkReady, setFlagshipSdkReady] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const { isLoading: isAuthLoading } = useAuth();
  const currentMemberQuery = useCurrentMember();
  const user = currentMemberQuery.data?.user as IMemberDetails;
  const { id: userId, country } = user ?? {};
  const visitorId = useVisitorId();

  const isCurrentMemberInitialLoading =
    currentMemberQuery.isLoading && currentMemberQuery.isFetching;

  const handleFlagshipInitDone = () => {
    setFlagshipSdkReady(true);
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Don't render FlagshipProvider during SSR to prevent hydration mismatch
  if (!isClient) {
    return <GlobalLoader fullHeight />;
  }

  return (
    <FlagshipProvider
      apiKey={FLAGSHIP_API_KEY}
      envId={FLAGSHIP_ENV_ID}
      visitorData={{
        id: userId ?? visitorId,
        isAuthenticated: !!userId,
        context: {
          country: country ?? '',
        },
      }}
      logLevel={5}
      enableClientCache={false}
      onUpdate={handleFlagshipInitDone}
    >
      {isAuthLoading || isCurrentMemberInitialLoading || !isFlagshipSdkReady ? (
        <GlobalLoader fullHeight />
      ) : (
        children
      )}
    </FlagshipProvider>
  );
};
