import type { RouteComponent } from '@remix-run/react/dist/routeModules';
import { PassThrough } from 'node:stream';
import { renderToString } from 'react-dom/server';
import type { EntryContext } from 'react-router';
import { ServerRouter } from 'react-router';
import { Locale } from 'types';

/**
 * Renders the head section of the HTML document during server-side rendering.
 * This function temporarily replaces the root component with a custom Head component
 * to generate the HTML for <head> section separately from the main body content.
 *
 * This is useful when you need to:
 * 1. Generate meta tags, title, and other head elements independently
 * 2. Ensure proper SEO by having complete control over head content
 * 3. Manage head content separately from the main application markup
 *
 * The function swaps the root component, renders it, and then restores the original
 * root component to maintain the application's integrity.
 */
export function renderHead({
  entryContext,
  request,
  Head,
}: {
  entryContext: EntryContext;
  request: Request;
  Head: RouteComponent;
}) {
  // create new context renders only <Head> and does not render errors
  const headContext = switchRootComponent(entryContext, Head);

  const head = renderToString(
    <ServerRouter context={headContext} url={request.url} />,
  );

  return head;
}

/**
 * Streams the complete HTML document by combining the head and body content.
 * This function writes the HTML structure in chunks using a PassThrough stream,
 * which is important for:
 * 1. Optimizing Time to First Byte (TTFB) through streaming
 * 2. Maintaining proper HTML structure with doctype, head, and body
 * 3. Adding special comment markers (<!--start head--> and <!--end head-->)
 *    that are used for client-side head management during hydration
 *
 * The special comments are used by removeOldHead() function to clean up
 * server-rendered head elements during client-side hydration.
 */
export function pipeBody({
  body,
  head,
  pipe,
  locale,
}: {
  body: PassThrough;
  head: string;
  pipe: (body: PassThrough) => void;
  locale: Locale;
}) {
  body.write(
    `<!DOCTYPE html>
    <html lang=${locale}>
      <head>
        <!--start head-->
        ${head}
        <!--end head-->
      </head>
      <body>`,
  );
  pipe(body);
  body.write(`</div></body></html>`);
}

/**
 * Removes old head elements from the document between special comment markers.
 * This function is used during client-side hydration to clean up server-rendered head elements
 * to prevent duplicate meta tags, title, etc. It looks for content between '<!-- start head -->'
 * and '<!-- end head -->' comments and removes everything in between including the comments themselves.
 * This ensures proper head management when transitioning from server to client rendering.
 *
 * @param parent - The parent element to search in (defaults to document.head)
 */
export function removeOldHead(parent: HTMLElement = document.head) {
  let foundOldHeader = false;
  const nodesToRemove: ChildNode[] = [];
  for (const node of parent.childNodes) {
    if (!foundOldHeader && node.nodeName !== '#comment') {
      continue;
    }
    if (
      foundOldHeader &&
      node.nodeName === '#comment' &&
      node.nodeValue === `end head`
    ) {
      nodesToRemove.push(node);
      break;
    }
    if (
      foundOldHeader ||
      (node.nodeName === '#comment' && node.nodeValue === `start head`)
    ) {
      foundOldHeader = true;
      nodesToRemove.push(node);
    }
  }
  for (const node of nodesToRemove) {
    node.remove();
  }
}

/**
 * Switches the root component of a Remix application while cleaning up error states.
 * This function is used during server-side rendering to:
 * 1. Replace the default root component with a custom Head component
 * 2. Remove any error states from both the server handoff string and static handler context
 * 3. Ensure a clean initial state when hydrating the client
 *
 * This is particularly useful when you want to customize the root layout of your
 * application while preventing error states from being passed to the client during hydration,
 * which could cause unwanted error UI to flash before the client-side routing takes over.
 *
 */
export function switchRootComponent(
  entryContext: EntryContext,
  Head: RouteComponent,
): EntryContext {
  let serverHandoffString = entryContext.serverHandoffString;
  if (serverHandoffString) {
    const serverHandoff = JSON.parse(serverHandoffString);
    // remove errors from JSON string
    delete serverHandoff?.state?.errors;
    serverHandoffString = JSON.stringify(serverHandoff);
  }

  return {
    ...entryContext,
    serverHandoffString,
    staticHandlerContext: {
      ...entryContext.staticHandlerContext,
      errors: null, // remove errors from context
    },
    routeModules: {
      ...entryContext.routeModules,
      root: {
        ...entryContext.routeModules.root,
        default: Head,
      },
    },
  };
}
