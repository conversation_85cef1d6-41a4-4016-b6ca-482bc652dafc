import { Locale } from 'types';

import { FALLBACK_LOCALE } from '~/utils/constants';

function formatStorageValue(value: number, lang = FALLBACK_LOCALE) {
  const baseUnit = lang === 'fr-FR' ? 'o' : 'B';

  if (value >= 1024) {
    return `${(value / 1024).toFixed(2)} T${baseUnit}`;
  }

  return `${value} G${baseUnit}`;
}

function formatMonth(month: number) {
  if (!month) {
    return '';
  }

  return month < 10 ? `0${month}` : month.toString();
}

export default {
  supportedLngs: Object.values(Locale),
  fallbackLng: FALLBACK_LOCALE,
  defaultNS: 'translation',
  ns: ['translation'],
  interpolation: {
    escapeValue: false, // Not needed for React as it escapes by default
    format: function (value: any, format: any, lang: any) {
      switch (format) {
        case 'storageUnit':
          return formatStorageValue(value, lang);
        case 'month':
          return formatMonth(value);
        default:
          return value;
      }
    },
  },
  keySeparator: '.',
  nsSeparator: ':',
  redirect: true,
  returnEmptyString: false,
  serializeConfig: false,
  react: {
    useSuspense: false, // Recommended setting for React 18+
  },
};
