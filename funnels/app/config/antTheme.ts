import { colorsPro, sizes, typos } from '@blade-group/design-tokens';
import { ThemeConfig } from 'antd';

// Static theme configuration for consistent SSR/client rendering
const colors = colorsPro.light;

export const antTheme: ThemeConfig = {
  cssVar: false,
  hashed: false, // Disable CSS hashing for consistent Chrome rendering
  token: {
    // Seed tokens
    borderRadius: sizes.radius.small, // Border radius of base components
    colorPrimary: colors.surface.primary, // Brand color
    colorError: colors.onsurface.error, // Used to represent the visual elements of the operation failure
    colorSuccess: colors.onsurface.success, // Used to represent the token sequence of operation success
    colorBgBase: colors.surface.backgroundLow, // Used to derive the base variable of the background color gradient
    colorTextBase: colors.onsurface.backgroundPrimary, // Used to derive the base variable of the text color gradient
    fontFamily: 'Nexa Text', // The font family of Ant Design
    controlHeight: 40, // The height of the basic controls such as buttons and input boxes in Ant Design
    colorWarning: colors.onsurface.warning, // Used to represent the warning map token
    fontSize: parseInt(typos.md.fontSize), // The most widely used font size in the design system
    colorInfo: colors.surface.secondaryDim, // Used to represent the operation information of the Token sequence
    colorLink: colors.surface.primary, // Control the color of hyperlink
    // fontFamilyCode: `'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace`, // Code font
    // lineType: 'solid', // Border style of base components
    lineWidth: 2, // Border width of base components
    // motion: true, // Used to configure the motion effect
    // motionBase: 0,
    // motionEaseInBack: 'cubic-bezier(0.71, -0.46, 0.88, 0.6)',
    // motionEaseInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',
    // motionEaseInOutCirc: 'cubic-bezier(0.78, 0.14, 0.15, 0.86)',
    // motionEaseInQuint: 'cubic-bezier(0.755, 0.05, 0.855, 0.06)',
    // motionEaseOut: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
    // motionEaseOutBack: 'cubic-bezier(0.12, 0.4, 0.29, 1.46)',
    // motionEaseOutCirc: 'cubic-bezier(0.08, 0.82, 0.17, 1)',
    // motionEaseOutQuint: 'cubic-bezier(0.23, 1, 0.32, 1)',
    // motionUnit: 0.1, // The unit of animation duration change
    // opacityImage: 1,
    // sizePopupArrow: 16, // The size of the component arrow
    // sizeStep: 4, // The base step of size change
    // sizeUnit: 4, // The unit of size change
    // wireframe: false, // Used to change the visual effect of the component to wireframe
    // zIndexBase: 0, // The base Z axis value of all components
    // zIndexPopupBase: 1000, // Base zIndex of component like FloatButton, Affix which can be cover by large popup

    // Map Tokens
    // borderRadiusOuter: 4, // Outer border radius
    // borderRadiusLG: sizes.radius.secondary, // LG size border radius, used in some large border radius components, such as Card, Modal and other components
    // borderRadiusSM: sizes.radius.tertiary, // SM size border radius, used in small size components, such as Button, Input, Select and other input components in small size
    // borderRadiusXS: sizes.radius.primary, // XS size border radius, used in some small border radius components, such as Segmented, Arrow and other components with small border radius
    // colorBgBlur: 'transparent', // Control the background color of frosted glass container, usually transparent
    colorBgContainer: colors.surface.backgroundLow, // Container background color, e.g: default button, input box, etc. Be sure not to confuse this with `colorBgElevated`

    // colorBgElevated: '#ffffff', // Container background color of the popup layer, in dark mode the color value of this token will be a little brighter than `colorBgContainer`. E.g: modal, pop-up, menu, etc.
    colorBgLayout: colors.surface.backgroundLowest, // This color is used for the background color of the overall layout of the page. This token will only be used when it is necessary to be at the B1 visual level in the page. Other usages are wrong
    // colorBgMask: 'rgba(0, 0, 0, 0.45)', // The background color of the mask, used to cover the content below the mask, Modal, Drawer and other components use this token
    // colorBgSpotlight: 'rgba(0, 0, 0, 0.85)', // This color is used to draw the user's strong attention to the background color, and is currently only used in the background color of Tooltip
    colorBorder: colors.onsurface.borderPrimary, // Default border color, used to separate different elements, such as: form separator, card separator, etc.
    colorBorderSecondary: colors.surface.backgroundHigh, // Slightly lighter than the default border color, this color is the same as `colorSplit`. Solid color is used
    // colorErrorActive: '#d9363e', // The active state of the error color
    colorErrorBg: colors.surface.error, // The background color of the error state
    // colorErrorBgHover: '#fff1f0', // The hover state background color of the error state
    // colorErrorBorder: '#ffccc7', // The border color of the error state
    // colorErrorBorderHover: '#ffa39e', // The hover state border color of the error state
    // colorErrorHover: '#ff7875', // The hover state of the error color
    colorErrorText: colors.onsurface.error, // The default state of the text in the error color
    // colorErrorTextActive: '#d9363e', // The active state of the text in the error color
    // colorErrorTextHover: '#ff7875', // The hover state of the text in the error color
    // colorFill: 'rgba(0, 0, 0, 0.15)', // The darkest fill color is used to distinguish between the second and third level of fill color, and is currently only used in the hover effect of Slider
    // colorFillQuaternary: 'rgba(0, 0, 0, 0.02)', // The weakest level of fill color is suitable for color blocks that are not easy to attract attention, such as zebra stripes, color blocks that distinguish boundaries, etc.
    // colorFillSecondary: 'rgba(0, 0, 0, 0.06)', // The second level of fill color can outline the shape of the element more clearly, such as Rate, Skeleton, etc. It can also be used as the Hover state of the third level of fill color, such as Table, etc.
    // colorFillTertiary: 'rgba(0, 0, 0, 0.04)', // The third level of fill color is used to outline the shape of the element, such as Slider, Segmented, etc. If there is no emphasis requirement, it is recommended to use the third level of fill color as the default fill color
    // colorInfoActive: '#0958d9', // Active state of dark color of information color
    // colorInfoBg: colors.surface.backgroundHigh, // Light background color of information color
    // colorInfoBgHover: '#bae0ff', // Hover state of light background color of information color
    // colorInfoBorder: '#91caff', // Border color of information color
    // colorInfoBorderHover: '#69b1ff', // Hover state of border color of information color
    // colorInfoHover: '#69b1ff', // Hover state of dark color of information color
    // colorInfoText: '#1677ff', // Default state of text color of information color
    // colorInfoTextActive: '#0958d9', // Active state of text color of information color
    // colorInfoTextHover: '#4096ff', // Hover state of text color of information color
    // colorLinkActive: '#0958d9', // Control the color of hyperlink when clicked
    // colorLinkHover: '#69b1ff', // Control the color of hyperlink when hovering
    // colorPrimaryActive: '#0958d9', // Dark active state under the main color gradient
    // colorPrimaryBg: '#e6f4ff', // Light background color of primary color, usually used for weak visual level selection state
    // colorPrimaryBgHover: '#bae0ff', // The hover state color corresponding to the light background color of the primary color
    // colorPrimaryBorder: '#91caff', // The stroke color under the main color gradient, used on the stroke of components such as Slider
    // colorPrimaryBorderHover: '#69b1ff', // The hover state of the stroke color under the main color gradient, which will be used when the stroke Hover of components such as Slider and Button
    // colorPrimaryHover: '#4096ff', // Hover state under the main color gradient
    // colorPrimaryText: '#1677ff', // Text color under the main color gradient
    // colorPrimaryTextActive: '#0958d9', // Active state of text color under the main color gradient
    // colorPrimaryTextHover: '#4096ff', // Hover state of text color under the main color gradient
    // colorSuccessActive: '#389e0d', // Active state color of dark success color
    colorSuccessBg: colors.surface.success, // Light background color of success color, used for Tag and Alert success state background color
    // colorSuccessBgHover: '#d9f7be', // Light background color of success color, but antd does not use this token currently
    // colorSuccessBorder: '#b7eb8f', // Border color of success color, used for Tag and Alert success state border color
    // colorSuccessBorderHover: '#95de64', // Hover state color of success color border
    // colorSuccessHover: '#95de64', // Hover state color of dark success color
    colorSuccessText: colors.onsurface.success, // Default state color of success color text
    // colorSuccessTextActive: '#389e0d', // Active state color of success color text
    // colorSuccessTextHover: '#73d13d', // Hover state color of success color text
    colorText: colors.onsurface.backgroundPrimary, // Default text color which comply with W3C standards, and this color is also the darkest neutral color
    // colorTextQuaternary: 'rgba(0, 0, 0, 0.25)', // The fourth level of text color is the lightest text color, such as form input prompt text, disabled color text, etc.
    // colorTextSecondary: 'rgba(0, 0, 0, 0.65)', // The second level of text color is generally used in scenarios where text color is not emphasized, such as label text, menu text selection state, etc.
    // colorTextTertiary: 'rgba(0, 0, 0, 0.45)', // The third level of text color is generally used for descriptive text, such as form supplementary explanation text, list descriptive text, etc.
    // colorWarningActive: '#d48806', // The active state of the warning color
    colorWarningBg: colors.surface.warning, // The background color of the warning state
    // colorWarningBgHover: '#fff1b8', // The hover state background color of the warning state
    // colorWarningBorder: '#ffe58f', // The border color of the warning state
    // colorWarningBorderHover: '#ffd666', // The hover state border color of the warning state
    // colorWarningHover: '#ffd666', // The hover state of the warning color
    colorWarningText: colors.onsurface.warning, // The default state of the text in the warning color
    // colorWarningTextActive: '#d48806', // The active state of the text in the warning color
    // colorWarningTextHover: '#ffc53d', // The hover state of the text in the warning color
    controlHeightLG: 50, // LG component height
    controlHeightSM: 24, // SM component height
    controlHeightXS: 16, // XS component height
    fontSizeHeading1: parseInt(typos.h2.fontSize), // Font size of h1 tag
    fontSizeHeading2: parseInt(typos.h4.fontSize), // Font size of h2 tag
    fontSizeHeading3: parseInt(typos.h5.fontSize), // Font size of h3 tag
    fontSizeHeading4: parseInt(typos.xl.fontSize), // Font size of h4 tag
    fontSizeHeading5: parseInt(typos.lg.fontSize), // Font size of h5 tag
    fontSizeLG: parseInt(typos.lg.fontSize), // Large font size
    fontSizeSM: parseInt(typos.sm.fontSize), // Small font size
    fontSizeXL: parseInt(typos.xl.fontSize), // Super large font size
    // lineHeight: parseFloat(typos.md.lineHeight), // Line height of text
    // lineHeightHeading1: parseFloat(typos.h1.lineHeight), // Line height of h1 tag
    // lineHeightHeading2: parseFloat(typos.h2.lineHeight), // Line height of h2 tag
    // lineHeightHeading3: parseFloat(typos.h3.lineHeight), // Line height of h3 tag
    // lineHeightHeading4: parseFloat(typos.h4.lineHeight), // Line height of h4 tag
    // lineHeightHeading5: 1.5, // Line height of h5 tag
    lineHeightLG: parseFloat(typos.lg.lineHeight), // Line height of large text
    lineHeightSM: parseFloat(typos.sm.lineHeight), // Line height of small text
    // lineWidthBold: 2, // The default line width of the outline class components, such as Button, Input, Select, etc.
    // motionDurationFast: '0.1s', // Motion speed, fast speed. Used for small element animation interaction
    // motionDurationMid: '0.2s', // Motion speed, medium speed. Used for medium element animation interaction
    // motionDurationSlow: '0.3s', // Motion speed, slow speed. Used for large element animation interaction
    // size: 16, // The basic size of the design system
    // sizeLG: 24, // LG size
    // sizeMD: 20, // MD size
    // sizeMS: 16, // MS size
    // sizeSM: 12, // SM size
    // sizeXL: 32, // XL size
    // sizeXS: 8, // XS size
    // sizeXXL: 48, // XXL size
    // sizeXXS: 4, // XXS size

    // Alias tokens
    // boxShadow:
    //   '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)', // Control the box shadow style of an element
    // boxShadowSecondary:
    //   '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)', // Control the secondary box shadow style of an element
    // boxShadowTertiary:
    //   '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)', // Control the tertiary box shadow style of an element
    // colorBgContainerDisabled: 'rgba(0, 0, 0, 0.04)', // Control the background color of container in disabled state
    // colorBgTextActive: 'rgba(0, 0, 0, 0.15)', // Control the background color of text in active state
    // colorBgTextHover: 'rgba(0, 0, 0, 0.06)', // Control the background color of text in hover state
    // colorBorderBg: '#ffffff', // Control the color of background border of element
    // colorErrorOutline: 'rgba(255, 38, 5, 0.06)', // Control the outline color of input component in error state
    // colorFillAlter: 'rgba(0, 0, 0, 0.02)', // Control the alternative background color of element
    // colorFillContent: 'rgba(0, 0, 0, 0.06)', // Control the background color of content area
    // colorFillContentHover: 'rgba(0, 0, 0, 0.15)', // Control the style of background color of content area when mouse hovers over it
    // colorHighlight: '#ff4d4f', // Control the color of page element when highlighted
    // colorIcon: 'rgba(0, 0, 0, 0.45)', // Weak action. Such as `allowClear` or Alert close button
    // colorIconHover: 'rgba(0, 0, 0, 0.88)', // Weak action hover color. Such as `allowClear` or Alert close button
    colorSplit: colors.surface.backgroundHigh, // Used as the color of separator, this color is the same as colorBorderSecondary but with transparency
    // colorTextDescription: 'rgba(0, 0, 0, 0.45)', // Control the font color of text description
    colorTextDisabled: colors.onsurface.backgroundPrimary, // Control the color of text in disabled state
    colorTextHeading: colors.onsurface.backgroundPrimary, // Control the font color of heading
    // colorTextLabel: 'rgba(0, 0, 0, 0.65)', // Control the font color of text label
    // colorTextLightSolid: '#fff', // Control the highlight color of text with background color, such as the text in Primary Button components
    colorTextPlaceholder: colors.onsurface.backgroundSecondary, // Control the color of placeholder text
    // colorWarningOutline: 'rgba(255, 215, 5, 0.1)', // Control the outline color of input component in warning state
    // controlInteractiveSize: 16, // Control the interactive size of control component
    // controlItemBgActive: '#e6f4ff', // Control the background color of control component item when active
    // controlItemBgActiveDisabled: 'rgba(0, 0, 0, 0.15)', // Control the background color of control component item when active and disabled
    // controlItemBgActiveHover: '#bae0ff', // Control the background color of control component item when hovering and active
    // controlItemBgHover: 'rgba(0, 0, 0, 0.04)', // Control the background color of control component item when hovering
    // controlOutline: 'rgba(5, 145, 255, 0.1)', // Control the outline color of input component
    // controlOutlineWidth: 2, // Control the outline width of input component
    // controlPaddingHorizontal: 12, // Control the horizontal padding of an element
    // controlPaddingHorizontalSM: 8, // Control the horizontal padding of an element with a small-medium size
    fontSizeIcon: parseInt(typos.sm.fontSize), // Control the font size of operation icon in Select, Cascader, etc. Normally same as fontSizeSM
    fontWeightStrong: typos.xl.fontWeight, // Control the font weight of heading components (such as h1, h2, h3) or selected item
    // lineWidthFocus: 4, // Control the width of the line when the component is in focus state
    // linkDecoration: 'none', // Control the text decoration style of a link
    // linkFocusDecoration: 'none', // Control the text decoration style of a link on focus
    // linkHoverDecoration: 'none', // Control the text decoration style of a link on mouse hover
    margin: sizes.spacing.small, // Control the margin of an element, with a medium size
    marginXXL: sizes.spacing.xxxLarge, // Control the margin of an element, with the largest size
    marginXL: sizes.spacing.xxLarge, // Control the margin of an element, with an extra-large size
    marginLG: sizes.spacing.xLarge, // Control the margin of an element, with a large size
    marginMD: sizes.spacing.large, // Control the margin of an element, with a medium-large size
    marginSM: sizes.spacing.small, // Control the margin of an element, with a medium-small size
    marginXS: sizes.spacing.xxSmall, // Control the margin of an element, with a small size
    marginXXS: sizes.spacing.tiny, // Control the margin of an element, with the smallest size
    opacityLoading: 0.3, // Control the opacity of the loading state
    padding: sizes.spacing.small, // Control the padding of the element
    // paddingContentHorizontal: 16, // Control the horizontal padding of content element
    // paddingContentHorizontalLG: 24, // Control the horizontal padding of content element, suitable for large screen devices
    // paddingContentHorizontalSM: 16, // Control the horizontal padding of content element, suitable for small screen devices
    // paddingContentVertical: 12, // Control the vertical padding of content element
    // paddingContentVerticalLG: 16, // Control the vertical padding of content element, suitable for large screen devices
    // paddingContentVerticalSM: 8, // Control the vertical padding of content element, suitable for small screen devices
    paddingXL: sizes.spacing.xxxLarge, // Control the extra large padding of the element
    paddingLG: sizes.spacing.xxLarge, // Control the large padding of the element
    paddingMD: sizes.spacing.large, // Control the medium padding of the element
    paddingSM: sizes.spacing.small, // Control the small padding of the element
    paddingXS: sizes.spacing.xxSmall, // Control the extra small padding of the element
    paddingXXS: sizes.spacing.tiny, // Control the extra extra small padding of the element
    screenLG: 992, // Control the screen width of large screens
    // screenLGMax: 1199, // Control the maximum width of large screens
    // screenLGMin: 992, // Control the minimum width of large screens
    screenMD: 768, // Control the screen width of medium screens
    // screenMDMax: 991, // Control the maximum width of medium screens
    // screenMDMin: 768, // Control the minimum width of medium screens
    screenSM: 576, // Control the screen width of small screens
    // screenSMMax: 767, // Control the maximum width of small screens
    // screenSMMin: 576, // Control the minimum width of small screens
    // screenXL: 1200, // Control the screen width of extra large screens
    // screenXLMax: 1599, // Control the maximum width of extra large screens
    // screenXLMin: 1200, // Control the minimum width of extra large screens
    // screenXS: 480, // Control the screen width of extra small screens
    // screenXSMax: 575, // Control the maximum width of extra small screens
    // screenXSMin: 480, // Control the minimum width of extra small screens
    // screenXXL: 1600, // Control the screen width of extra extra large screens
    // screenXXLMin: 1600, // Control the minimum width of extra extra large screens
  },
  components: {
    Alert: {
      lineWidth: 1,
    },
    Typography: {
      titleMarginBottom: 0,
      titleMarginTop: 0,
    },
    Button: {
      fontSize: 16,
      lineHeight: 19,
      fontSizeLG: 28,
      lineHeightLG: 40,
      fontSizeSM: 14,
      lineHeightSM: 16,

      onlyIconSize: 16,
      onlyIconSizeLG: 28,
      onlyIconSizeSM: 14,

      dangerShadow: 'unset',
      defaultShadow: 'unset',
      borderRadius: 0,
      contentFontSizeSM: 14,
      fontWeight: 300,
      contentFontSizeLG: parseInt(typos.h5.fontSize),

      borderColorDisabled: colors.surface.backgroundHighest,
      colorBgContainerDisabled: colors.surface.backgroundHighest,
    },
    Checkbox: {
      // paddingXS: 8,
      fontSize: 12,
    },
    Menu: {
      itemBg: colors.surface.backgroundLow,
      colorBgContainer: colors.surface.backgroundLow,
      itemSelectedBg: colors.surface.backgroundHigh,
      itemHoverBg: colors.surface.backgroundHighest,
      itemActiveBg: colors.surface.backgroundHigh,
      itemSelectedColor: colors.onsurface.secondary,
      itemHoverColor: colors.onsurface.secondary,
      subMenuItemBg: colors.surface.backgroundLow,
      itemMarginBlock: 0,
      itemMarginInline: 0,
      itemBorderRadius: 0,
      activeBarBorderWidth: 0,
    },
    Modal: {
      padding: 12,
    },
    Divider: {
      marginXXS: 0,
      marginXS: 0,
      marginSM: 0,
      margin: 0,
      marginMD: 0,
      marginLG: 0,
      marginXL: 0,
      marginXXL: 0,
      lineWidth: 1,
    },
    Drawer: {
      colorBgElevated: colors.surface.backgroundLow,
      footerPaddingBlock: 0,
      footerPaddingInline: 0,
    },
    Layout: {
      headerBg: colors.surface.backgroundLow,
      bodyBg: colors.surface.backgroundLowest,
      siderBg: colors.surface.backgroundLow,
    },
    Skeleton: {
      controlHeightSM: sizes.spacing.xSmall,
      controlHeight: sizes.spacing.large,
    },
    Input: {
      borderRadius: sizes.radius.xxSmall,
      activeBorderColor: colors.onsurface.secondary,
      hoverBorderColor: colors.onsurface.secondary,
      activeBg: colors.surface.backgroundHigh,
      activeShadow: 'unset',
      errorActiveShadow: 'unset',
      warningActiveShadow: 'unset',
    },
    Select: {
      fontSize: 16,
      padding: 50,
      paddingContentVertical: sizes.spacing.small,
      paddingContentHorizontal: sizes.spacing.small,
      borderRadius: sizes.radius.xxSmall,
      activeBorderColor: colors.onsurface.secondary,
      hoverBorderColor: colors.onsurface.secondary,
      // activeBg: colors.surface.backgroundHigh,
      // activeShadow: 'unset',
      optionSelectedBg: colors.surface.backgroundHigh,
    },
    Table: {
      borderColor: colors.surface.backgroundLowest,
      // cellFontSizeMD
      // cellFontSizeSM
      cellPaddingBlock: sizes.spacing.small,
      cellPaddingInline: sizes.spacing.small,
      // cellPaddingBlockMD
      // cellPaddingInlineMD
      // cellPaddingBlockSM
      // cellPaddingInlineSM
      headerBg: colors.surface.backgroundHigh,
      headerSortHoverBg: colors.surface.backgroundHighest,
      headerSortActiveBg: colors.surface.backgroundHighest,
      headerSplitColor: colors.onsurface.borderPrimary,
      rowHoverBg: colors.surface.backgroundHigh,
      rowSelectedBg: colors.surface.backgroundHigh,
      bodySortBg: colors.surface.backgroundLow,
    },
    Tooltip: {
      colorBgSpotlight: colors.surface.backgroundHigh,
      colorTextLightSolid: colors.onsurface.backgroundPrimary,
      paddingSM: sizes.spacing.small,
      paddingXS: sizes.spacing.small,
    },
  },
};
