import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ConfigProvider } from 'antd';
import { useEffect, useState } from 'react';
import { CookiesProvider } from 'react-cookie';
import { AuthProvider } from 'react-oidc-context';
import {
  data,
  Links,
  LoaderFunctionArgs,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from 'react-router';
import { useChangeLanguage } from 'remix-i18next/react';
import { getClientIPAddress } from 'remix-utils/get-client-ip-address';
import { IS_DEV } from 'utils';

import './styles/global.css';
import 'antd/dist/reset.css';

import ClientOnly from '~/components/ClientOnly/ClientOnly';
import ConsentBanner from '~/components/ConsentBanner/ConsentBanner';
import { GeolocalizeUser } from '~/components/GeolocalizeUser/GeolocalizeUser';
import { HTMLLangSetter } from '~/components/HTMLLangSetter/HTMLLangSetter';
import { AppLayout } from '~/components/Layout/AppLayout';
import { antTheme } from '~/config/antTheme';
import { oidcConfig } from '~/config/oauth2-config';
import { FlagshipContext } from '~/contexts/FlagshipContext';
import { TrackingProvider } from '~/contexts/TrackingContext';
import i18nServer, { localeCookie } from '~/modules/i18n.server';
import useStore from '~/store';
import { setDatepickerLocale } from '~/utils/datepickerLocale';
import { initializeDayjs } from '~/utils/dayjs';

initializeDayjs();

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000, // At least 30 seconds of default stale time
      refetchOnWindowFocus: false,
      retry: 3,
    },
  },
});

// Sets the locale and headers for the root component
export async function loader({ request }: LoaderFunctionArgs) {
  const locale = await i18nServer.getLocale(request);
  const socketIpAddress = getClientIPAddress(request);

  return data(
    { locale, socketIpAddress },
    { headers: { 'Set-Cookie': await localeCookie.serialize(locale) } },
  );
}

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en-US">
      <head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="icon" type="image/png" href="/funnel/favicon.ico" />
        <script async src="https://pay.google.com/gp/p/js/pay.js"></script>
        <script async src="https://js.chargebee.com/v2/chargebee.js"></script>
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

const InnerRoot = ({ locale }: any) => {
  return (
    <AuthProvider {...oidcConfig}>
      <QueryClientProvider client={queryClient}>
        <FlagshipContext>
          <TrackingProvider>
            <ClientOnly>{() => <HTMLLangSetter />}</ClientOnly>
            <ConfigProvider theme={antTheme} locale={locale}>
              <CookiesProvider>
                <AppLayout>
                  <GeolocalizeUser>
                    <ClientOnly>{() => <ConsentBanner />}</ClientOnly>
                    <Outlet />
                  </GeolocalizeUser>
                </AppLayout>
              </CookiesProvider>
            </ConfigProvider>
          </TrackingProvider>
        </FlagshipContext>
        <ClientOnly>
          {() => IS_DEV && <ReactQueryDevtools initialIsOpen={false} />}
        </ClientOnly>
      </QueryClientProvider>
    </AuthProvider>
  );
};

export default function Root({ loaderData }: any) {
  // Initialize locale with the server-provided value to prevent hydration mismatch
  const [locale, setLocale] = useState<any>(() =>
    setDatepickerLocale(loaderData.locale),
  );
  useChangeLanguage(loaderData.locale);

  useEffect(() => {
    setLocale(setDatepickerLocale(loaderData.locale));
  }, [loaderData.locale]);

  const { setUserIp } = useStore();

  useEffect(() => {
    // Set IP address after hydration to prevent server/client mismatch
    const ipAddress =
      loaderData.socketIpAddress ||
      (import.meta.env.DEV ? '*************' : null);
    if (ipAddress) {
      setUserIp(ipAddress);
    }
  }, [loaderData.socketIpAddress, setUserIp]);

  return <InnerRoot locale={locale} />;
}
