import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ConfigProvider } from 'antd';
import { useEffect, useState } from 'react';
import { CookiesProvider } from 'react-cookie';
import { AuthProvider } from 'react-oidc-context';
import {
  data,
  Links,
  LoaderFunctionArgs,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from 'react-router';
import { useChangeLanguage } from 'remix-i18next/react';
import { getClientIPAddress } from 'remix-utils/get-client-ip-address';

import './styles/global.css';
import 'antd/dist/reset.css';

import ClientOnly from '~/components/ClientOnly/ClientOnly';
import ConsentBanner from '~/components/ConsentBanner/ConsentBanner';
import { GeolocalizeUser } from '~/components/GeolocalizeUser/GeolocalizeUser';
import { HTMLLangSetter } from '~/components/HTMLLangSetter/HTMLLangSetter';
import { AppLayout } from '~/components/Layout/AppLayout';
import { antTheme } from '~/config/antTheme';
import { oidcConfig } from '~/config/oauth2-config';
import { FlagshipContext } from '~/contexts/FlagshipContext';
import { TrackingProvider } from '~/contexts/TrackingContext';
import i18nServer, { localeCookie } from '~/modules/i18n.server';
import useStore from '~/store';
import { setDatepickerLocale } from '~/utils/datepickerLocale';
import { initializeDayjs } from '~/utils/dayjs';

initializeDayjs();

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000, // At least 30 seconds of default stale time
      refetchOnWindowFocus: false,
      retry: 3,
    },
  },
});

// Sets the locale and headers for the root component
export async function loader({ request }: LoaderFunctionArgs) {
  const locale = await i18nServer.getLocale(request);
  const socketIpAddress = getClientIPAddress(request);

  return data(
    {
      locale: locale || 'en-GB', // Ensure we always have a locale
      socketIpAddress: socketIpAddress || null, // Explicitly set null if no IP
    },
    {
      headers: {
        'Set-Cookie': await localeCookie.serialize(locale || 'en-GB'),
      },
    },
  );
}

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="icon" type="image/png" href="/funnel/favicon.ico" />
        <script async src="https://pay.google.com/gp/p/js/pay.js"></script>
        <script async src="https://js.chargebee.com/v2/chargebee.js"></script>
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

const InnerRoot = ({ locale }: any) => {
  return (
    <AuthProvider {...oidcConfig}>
      <QueryClientProvider client={queryClient}>
        <FlagshipContext>
          <TrackingProvider>
            <ConfigProvider theme={antTheme} locale={locale}>
              <ClientOnly>
                <CookiesProvider>
                  <HTMLLangSetter />
                  <AppLayout>
                    <GeolocalizeUser>
                      <ConsentBanner />
                      <Outlet />
                    </GeolocalizeUser>
                  </AppLayout>
                </CookiesProvider>
              </ClientOnly>
            </ConfigProvider>
          </TrackingProvider>
        </FlagshipContext>
        <ClientOnly>
          {typeof window !== 'undefined' &&
            window.location.hostname === 'localhost' && (
              <ReactQueryDevtools initialIsOpen={false} />
            )}
        </ClientOnly>
      </QueryClientProvider>
    </AuthProvider>
  );
};

export default function Root({ loaderData }: any) {
  // Initialize locale with the server-provided value to prevent hydration mismatch
  // Ensure we always have a valid locale value with fallback
  const initialLocale = loaderData?.locale || 'en-GB';
  const [locale, setLocale] = useState<any>(() => {
    try {
      return setDatepickerLocale(initialLocale);
    } catch {
      // Fallback if setDatepickerLocale fails
      return null;
    }
  });
  useChangeLanguage(initialLocale);

  useEffect(() => {
    if (loaderData?.locale) {
      try {
        setLocale(setDatepickerLocale(loaderData.locale));
      } catch {
        // Keep current locale if setting fails
        // eslint-disable-next-line no-console
        console.warn('Failed to set datepicker locale:', loaderData.locale);
      }
    }
  }, [loaderData?.locale]);

  const { setUserIp } = useStore();

  useEffect(
    () => {
      // Only set IP address if we have one from the server
      // This prevents hydration mismatches when IP detection fails
      if (loaderData?.socketIpAddress) {
        setUserIp(loaderData.socketIpAddress);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [loaderData?.socketIpAddress],
  );

  return <InnerRoot locale={locale} />;
}
