import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ConfigProvider } from 'antd';
import { FC, useEffect, useState } from 'react';
import { CookiesProvider } from 'react-cookie';
import { createPortal } from 'react-dom';
import { AuthProvider } from 'react-oidc-context';
import {
  data,
  Links,
  LoaderFunctionArgs,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from 'react-router';
import { useChangeLanguage } from 'remix-i18next/react';
import { ClientOnly } from 'remix-utils/client-only';
import { getClientIPAddress } from 'remix-utils/get-client-ip-address';
import { IS_DEV } from 'utils';

import './styles/global.css';
import 'antd/dist/reset.css';

import ConsentBanner from '~/components/ConsentBanner/ConsentBanner';
import { GeolocalizeUser } from '~/components/GeolocalizeUser/GeolocalizeUser';
import { HTMLLangSetter } from '~/components/HTMLLangSetter/HTMLLangSetter';
import { AppLayout } from '~/components/Layout/AppLayout';
import { antTheme } from '~/config/antTheme';
import { oidcConfig } from '~/config/oauth2-config';
import { FlagshipContext } from '~/contexts/FlagshipContext';
import { TrackingProvider } from '~/contexts/TrackingContext';
import i18nServer, { localeCookie } from '~/modules/i18n.server';
import useStore from '~/store';
import { setDatepickerLocale } from '~/utils/datepickerLocale';
import { initializeDayjs } from '~/utils/dayjs';

initializeDayjs();

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000, // At least 30 seconds of default stale time
      refetchOnWindowFocus: false,
      retry: 3,
    },
  },
});

// Sets the locale and headers for the root component
export async function loader({ request }: LoaderFunctionArgs) {
  const locale = await i18nServer.getLocale(request);
  const socketIpAddress = getClientIPAddress(request);

  return data(
    { locale, socketIpAddress },
    { headers: { 'Set-Cookie': await localeCookie.serialize(locale) } },
  );
}

export const Head = () => (
  <>
    <meta charSet="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/png" href="/funnel/favicon.ico" />
    <script async src="https://pay.google.com/gp/p/js/pay.js"></script>
    <script async src="https://js.chargebee.com/v2/chargebee.js"></script>
    <Meta />
    <Links />
  </>
);

const Providers: FC<{ locale: any; children: React.ReactNode }> = ({
  children,
  locale,
}) => {
  return (
    <AuthProvider {...oidcConfig}>
      <QueryClientProvider client={queryClient}>
        <FlagshipContext>
          <TrackingProvider>
            <HTMLLangSetter />
            <ConfigProvider theme={antTheme} locale={locale}>
              <CookiesProvider>
                <AppLayout>
                  <GeolocalizeUser>
                    <ConsentBanner />
                    {children}
                  </GeolocalizeUser>
                </AppLayout>
              </CookiesProvider>
            </ConfigProvider>
          </TrackingProvider>
        </FlagshipContext>
        {IS_DEV && <ReactQueryDevtools initialIsOpen={false} />}
      </QueryClientProvider>
    </AuthProvider>
  );
};

const InnerRoot = ({ locale }: any) => {
  return (
    <>
      <ClientOnly>{() => createPortal(<Head />, document.head)}</ClientOnly>
      <Providers locale={locale}>
        <Outlet />
      </Providers>
      <ScrollRestoration />
      <Scripts />
    </>
  );
};

export default function Root({ loaderData }: any) {
  const [locale, setLocale] = useState<any>('en-US');
  useChangeLanguage(loaderData.locale);

  useEffect(() => {
    setLocale(setDatepickerLocale(loaderData.locale));
  }, [loaderData.locale]);

  const { setUserIp } = useStore();

  useEffect(() => {
    // In DEV environment, we use the Shadow mocked IP address
    if (import.meta.env.DEV && !loaderData.socketIpAddress) {
      setUserIp('*************');
    } else {
      setUserIp(loaderData.socketIpAddress);
    }
  }, [loaderData.socketIpAddress, setUserIp]);

  return <InnerRoot locale={locale} />;
}
