/* eslint-disable no-console */
import i18next from 'i18next';
import I18nextBrowserLanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';
import { startTransition, StrictMode } from 'react';
import { hydrateRoot } from 'react-dom/client';
import { I18nextProvider, initReactI18next } from 'react-i18next';
import { HydratedRouter } from 'react-router/dom';

import i18n from '~/config/i18n';
import { canHydrate, waitForDOM } from '~/utils/hydration';

async function hydrate() {
  try {
    // Wait for DOM to be ready
    await waitForDOM();

    // Check if hydration is possible
    if (!canHydrate()) {
      console.warn('DOM not ready for hydration, retrying...');
      setTimeout(hydrate, 100);
      return;
    }

    // Get the language from the HTML lang attribute set by the server
    // This ensures client uses the same language as server
    const htmlLang = document.documentElement.lang || 'en-US';

    // eslint-disable-next-line import/no-named-as-default-member
    await i18next
      .use(initReactI18next) // Tell i18next to use the react-i18next plugin
      .use(I18nextBrowserLanguageDetector) // Setup a client-side language detector
      .use(Backend)
      .init({
        ...i18n,
        lng: htmlLang, // Use the same language as server
        supportedLngs: i18n.supportedLngs,
        ns: i18n.ns || ['translation'],
        backend: { loadPath: '/funnel/locales/{{ns}}-{{lng}}.json' },
        fallbackLng: 'en-US',
        debug: false,
        interpolation: {
          escapeValue: false,
        },
        detection: {
          // Here only enable htmlTag detection, we'll detect the language only
          // server-side with remix-i18next, by using the `<html lang>` attribute
          // we can communicate to the client the language detected server-side
          order: ['htmlTag'],
          // Because we only use htmlTag, there's no reason to cache the language
          // on the browser, so we disable it
          caches: [],
        },
      });

    startTransition(() => {
      hydrateRoot(
        document,
        <StrictMode>
          <I18nextProvider i18n={i18next}>
            <HydratedRouter />
          </I18nextProvider>
        </StrictMode>,
      );
    });
  } catch (error) {
    console.error('Hydration failed:', error);
    // Log additional debugging information
    console.error('DOM ready state:', document.readyState);
    console.error('Can hydrate:', canHydrate());

    // Fallback: try again after a delay
    setTimeout(() => {
      try {
        startTransition(() => {
          hydrateRoot(
            document,
            <StrictMode>
              <I18nextProvider i18n={i18next}>
                <HydratedRouter />
              </I18nextProvider>
            </StrictMode>,
          );
        });
      } catch (fallbackError) {
        console.error('Fallback hydration also failed:', fallbackError);
      }
    }, 1000);
  }
}

if (window.requestIdleCallback) {
  window.requestIdleCallback(hydrate);
} else {
  // Safari doesn't support requestIdleCallback
  // https://caniuse.com/requestidlecallback
  window.setTimeout(hydrate, 1);
}
