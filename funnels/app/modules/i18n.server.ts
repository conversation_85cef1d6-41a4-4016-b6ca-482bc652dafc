import Backend from 'i18next-fs-backend';
import { resolve } from 'node:path';
import { createCookie } from 'react-router';
import { RemixI18Next } from 'remix-i18next/server';
import { Locale } from 'types';

import i18n from '~/config/i18n';
import { DEFAULT_LOCALE } from '~/utils/constants';

export const localeCookie = createCookie('lng', {
  path: '/',
  sameSite: 'lax',
  secure: process.env.NODE_ENV === 'production',
  httpOnly: true,
});

class CustomRemixI18Next extends RemixI18Next {
  async getLocale(request: Request) {
    // Extract locale from URL path using a similar pattern to React Router
    const url = new URL(request.url);
    const pathname = url.pathname;

    // Simple regex-based path matching for "/:locale/*" pattern
    const localeMatch = pathname.match(/\/([a-z]{2}-[A-Z]{2})(\/|$)/);

    if (localeMatch && localeMatch[1]) {
      const localeCandidate = localeMatch[1];

      // Verify it's in our supported locales
      if (Object.values(Locale).includes(localeCandidate as Locale)) {
        return localeCandidate;
      }
    }

    // Fall back to the default detection methods
    return super.getLocale(request);
  }
}

export default new CustomRemixI18Next({
  detection: {
    supportedLanguages: i18n.supportedLngs,
    fallbackLanguage: DEFAULT_LOCALE,
    cookie: localeCookie,
  },
  // This is the configuration for i18next used
  // when translating messages server-side only
  i18next: {
    ...i18n,
    ns: i18n.ns,
    backend: {
      loadPath: resolve('./funnels/public/locales/{{ns}}-{{lng}}.json'),
    },
  },
  plugins: [Backend],
});
