import { useEffect } from 'react';

import { GlobalLoader } from '~/components/ui/Loader';
import { useGeoLocalization } from '~/hooks/reactQuery/geoLocalization/useGeoLocalization';
import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';
import { ZIPCODE_PER_MARKET } from '~/utils/constants';

export const GeolocalizeUser = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { country } = useLocale();
  const { data: geolocalizationData } = useGeoLocalization();
  const { geolocResults, setGeolocResults } = useStore();

  useEffect(
    () => {
      const payload = { geolocalized: true, zipcode: '', ip: '' };

      const defaultZipcode = ZIPCODE_PER_MARKET[country];
      const doesCountryMatch =
        geolocalizationData?.country_iso?.toLowerCase() === country;

      const zipcode =
        (doesCountryMatch ? geolocalizationData?.zip_code : undefined) ??
        defaultZipcode;

      payload.zipcode = zipcode;
      payload.ip = geolocalizationData?.ip ?? '';

      setGeolocResults(payload.geolocalized, payload.zipcode, payload.ip);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      geolocResults?.geolocalized,
      geolocalizationData?.ip,
      geolocalizationData?.country_iso,
      geolocalizationData?.zip_code,
      country,
    ],
  );

  return !geolocResults?.geolocalized ? <GlobalLoader fullHeight /> : children;
};
