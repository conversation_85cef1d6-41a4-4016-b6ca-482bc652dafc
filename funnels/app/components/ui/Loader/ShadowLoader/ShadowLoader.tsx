import Lottie from 'lottie-react';
import { useState, useEffect } from 'react';

import useStyles from '~/components/ui/Loader/ShadowLoader/ShadowLoader.styles';
import { useDelayBeforeDisplay } from '~/hooks/useDelayBeforeDisplay';

interface Props {
  color?: string;
  isPathVisible?: boolean;
  size?: number;
  sizeStroke?: number;
}

export const ShadowLoader = ({
  color = undefined,
  isPathVisible = true,
  size = undefined,
  sizeStroke = undefined,
}: Props) => {
  const [animationData, setAnimationData] = useState<any>(null);
  const [isClient, setIsClient] = useState(false);

  const { styles } = useStyles({
    color: color,
    isPathVisible: isPathVisible,
    size: size,
    sizeStroke: sizeStroke,
  });

  // When loaders are displayed directly the user perception of slowness is increased
  // We delay the display of the loader to avoid that
  const showLoader = useDelayBeforeDisplay();

  useEffect(() => {
    setIsClient(true);
    // Load animation data only on client side
    if (typeof window !== 'undefined') {
      import('~/components/ui/Loader/ShadowLoader/shadowLoaderAnimation.json')
        .then(data => {
          setAnimationData(data.default || data);
        })
        .catch(() => {
          // Silently fail and use fallback
        });
    }
  }, []);

  const renderLoader = () => {
    if (isClient && animationData) {
      try {
        return <Lottie animationData={animationData} loop={true} />;
      } catch (error) {
        // Fall back to simple loader
      }
    }

    // Fallback simple loader
    return null;
  };

  return showLoader ? (
    <div className={styles.shadowLoader}>{renderLoader()}</div>
  ) : null;
};
