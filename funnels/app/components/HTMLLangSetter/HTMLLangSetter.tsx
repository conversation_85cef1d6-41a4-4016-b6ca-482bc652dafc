import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';

export const HTMLLangSetter = () => {
  const { i18n } = useTranslation();
  const [language, setLanguage] = useState('en');

  useEffect(() => {
    // Only update language after hydration to prevent mismatch
    if (i18n.language) {
      setLanguage(i18n.language);
    }
  }, [i18n.language]);

  return <Helmet htmlAttributes={{ lang: language }} />;
};
