import { useEffect } from 'react';
import { useCallback } from 'react';
import { useCookies } from 'react-cookie';
import { useParams, useNavigate, useLocation } from 'react-router';
import { Language, Locale, Market } from 'types';
import { REGION_PER_MARKET } from 'utils';

import {
  COOKIE_DOMAIN,
  DEFAULT_LOCALE,
  LANGUAGES_PER_MARKET,
} from '~/utils/constants';

export const useLocale = () => {
  const [cookies, setCookies] = useCookies(['NEXT_LOCALE']);
  const params = useParams();
  const { pathname: currentPath } = useLocation();
  const navigate = useNavigate();

  const locale = params.locale;

  const currentLocale = !locale || locale === Locale.EN ? Locale.EN_US : locale;
  const [language, country] = currentLocale.split('-') as [
    Language,
    Uppercase<Market>,
  ];
  const region = country
    ? REGION_PER_MARKET[country.toLowerCase() as Market]
    : undefined;

  if (!country || !language || !region) {
    throw "Couldn't compute country, language or region from locale.";
  }

  useEffect(() => {
    if (!cookies.NEXT_LOCALE) {
      setCookies('NEXT_LOCALE', currentLocale, {
        path: '/',
        domain: COOKIE_DOMAIN,
      });
    }
  }, [currentLocale, cookies.NEXT_LOCALE, setCookies]);

  const switchLocale = useCallback(
    (newLocale: Locale = DEFAULT_LOCALE) => {
      if (newLocale === currentLocale && locale !== Locale.EN) {
        return;
      }

      if (cookies.NEXT_LOCALE !== newLocale) {
        setCookies('NEXT_LOCALE', newLocale, {
          path: '/',
          domain: COOKIE_DOMAIN,
        });
      }

      // Close chat and change liveChat visibility to force update current group
      if (window.LC_API && window.LiveChatWidget) {
        window.LC_API.close_chat?.();
        window.LiveChatWidget.call?.('hide');
        window.LiveChatWidget.call?.('minimize');
      }

      const newPath = currentPath.replace(
        new RegExp(`${locale}`, 'g'),
        newLocale,
      );

      navigate(newPath);
    },
    [
      locale,
      navigate,
      currentLocale,
      currentPath,
      cookies.NEXT_LOCALE,
      setCookies,
    ],
  );

  const resetAfterMarketChange = (currentMarket: Market) => {
    const defaultLanguage =
      LANGUAGES_PER_MARKET[currentMarket][0] ?? Language.EN;
    const newLocale = `${defaultLanguage.toUpperCase()}_${currentMarket.toUpperCase()}`;
    const userLocale = Locale[newLocale as keyof typeof Locale];

    switchLocale(userLocale);
  };

  return {
    country: country.toLowerCase() as Market,
    language: language.toLowerCase() as Language,
    locale: currentLocale as Locale,
    region: region,
    switchLocale,
    resetAfterMarketChange,
  };
};
