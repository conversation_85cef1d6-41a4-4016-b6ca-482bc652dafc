import { useEffect, useState } from 'react';

export function useDelayBeforeDisplay(delay = 100) {
  // Start with true during SSR to prevent hydration mismatch
  // The delay only applies on the client side for UX purposes
  const [shouldDisplayComponent, setShouldDisplayComponent] = useState(true);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    // Reset to false initially on client, then show after delay
    setShouldDisplayComponent(false);

    const timeoutId = setTimeout(() => {
      setShouldDisplayComponent(true);
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [delay]);

  // During SSR, always show to prevent hydration mismatch
  // On client, respect the delay for UX
  return !isClient || shouldDisplayComponent;
}
