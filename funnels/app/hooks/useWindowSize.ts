import { useState, useLayoutEffect } from 'react';

export const useWindowSize = (
  updateOnResize = false,
): { width: number; height: number } => {
  const [size, setSize] = useState({ width: 0, height: 0 });

  useLayoutEffect(() => {
    const updateSize = (): void => {
      setSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateSize();

    if (updateOnResize) {
      window.addEventListener('resize', updateSize);
      return () => window.removeEventListener('resize', updateSize);
    }
  }, [updateOnResize]);

  return size;
};
