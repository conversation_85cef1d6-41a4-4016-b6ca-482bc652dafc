import { useEffect, useState } from 'react';

declare let google: any; // TODO: find a better way to do this

export const useIsPaymentMeanAvailableOnDevice = () => {
  const [isApplePayAvailableOnDevice, setIsApplePayAvailableOnDevice] =
    useState(false);
  const [isGooglePayAvailableOnDevice, setIsGooglePayAvailableOnDevice] =
    useState(false);

  const checkApplePayAvailability = () => {
    setIsApplePayAvailableOnDevice(!!window.ApplePaySession);
  };

  const checkGooglePayAvailability = async () => {
    // Dummy informations used here to check if google pay is available on current device
    const client = new google.payments.api.PaymentsClient({
      environment: 'TEST',
      merchantInfo: {
        merchantId: '12345678901234567890',
        merchantName: 'Demo Merchant',
      },
    });

    const readyToPayOptions = {
      apiVersion: 2,
      apiVersionMinor: 0,
      allowedPaymentMethods: [
        {
          type: 'CARD',
          parameters: {
            allowedAuthMethods: ['PAN_ONLY', 'CRYPTOGRAM_3DS'],
            allowedCardNetworks: ['MASTERCARD', 'VISA'],
          },
          tokenizationSpecification: {
            type: 'PAYMENT_GATEWAY',
            parameters: {
              gateway: 'example',
            },
          },
        },
      ],
    };
    client.isReadyToPay(readyToPayOptions).then((res: any) => {
      setIsGooglePayAvailableOnDevice(res.result);
    });
  };

  useEffect(() => {
    checkApplePayAvailability();
    checkGooglePayAvailability();
  }, []);

  return { isApplePayAvailableOnDevice, isGooglePayAvailableOnDevice };
};
