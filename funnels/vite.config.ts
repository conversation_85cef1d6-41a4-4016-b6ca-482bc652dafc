import { reactRouter } from '@react-router/dev/vite';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import commonjs from 'vite-plugin-commonjs';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  return {
    base: '/funnel/',
    plugins: [
      reactRouter(),
      tsconfigPaths(),
      commonjs(),
      sentryVitePlugin({
        authToken: process.env.SENTRY_AUTH_TOKEN,
        org: 'cbp',
        project: 'cbp-frontend-funnels',
      }),
    ],
    server: {
      watch: {
        usePolling: true,
      },
      host: true, // Allows the container to be accessed from outside
      strictPort: true,
      port: 3002,
    },
    build: {
      outDir: 'build',
      emptyOutDir: true,
      sourcemap: false,
      minify: 'esbuild',
      esbuild: {
        drop: ['console', 'debugger'],
        legalComments: 'none',
      },
      target: 'es2020',
      cssCodeSplit: false,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            antd: ['antd'],
          },
        },
      },
    },
    optimizeDeps: {
      include: ['lodash', 'immer'],
    },
    resolve: {
      alias: {
        lodash: 'lodash-es',
        '@': path.resolve(__dirname, ''),
        '@/*': path.resolve(__dirname, '*'),
        '~/*': path.resolve(__dirname, './app/*'),
        utils: path.resolve(__dirname, '../utils/index.ts'),
      },
    },
    define: {
      'process.env.VITE_PUBLIC_API_URL': JSON.stringify(
        env.VITE_PUBLIC_API_URL,
      ),
      'process.env.NODE_ENV': JSON.stringify(mode),
    },
    ssr: {
      noExternal: [
        '@blade-group/shade',
        '@flagship.io/react-sdk',
        '@flagship.io/js-sdk',
        'antd',
        '@ant-design/icons',
        'antd-style',
        'lottie-react',
      ],
    },
    test: {
      coverage: {
        exclude: ['./mocks/browser.ts', './mocks/handlers.ts'],
        thresholds: {},
      },
    },
  };
});
