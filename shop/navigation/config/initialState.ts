import { ProductFamilyId } from 'types';

import { NavigationState } from '../types';
import { createState } from '../utils/createState';

import { combineGuards, options } from '@/navigation/options';
import { ShopFunnel } from '@/types/shop';

export const initialState = createState<NavigationState.InitialState>(
  {
    on: {
      'initialState.NEXT': [
        // Handle redirection to other websites
        {
          cond: combineGuards.and(
            options.guards.isFunnelFn(ShopFunnel.B2C_MAIN),
            options.guards.hasFeatureFlagFn(
              'shop_to_funnel',
              flagValue => !!flagValue,
            ),
          ),
          target: NavigationState.OutsideRedirections,
        },
        {
          cond: combineGuards.and(
            options.guards.isFunnelFn(ShopFunnel.B2C_BUY_FROM_ACCESS_CODE),
            ctx => !ctx.user,
          ),
          target: NavigationState.LoggingIn,
        },
        {
          cond: options.guards.isFunnelFn(ShopFunnel.B2C_BUY_FROM_ACCESS_CODE),
          target: NavigationState.CheckingAccessCode,
        },
        {
          cond: options.guards.isFunnelFn(ShopFunnel.UPDATE_STORAGE),
          target: NavigationState.UpdatingStorage,
        },
        {
          cond: options.guards.isFunnelFn(ShopFunnel.UPDATE_DRIVE_GROUP),
          target: NavigationState.UpdatingDriveGroup,
        },
        {
          cond: options.guards.isFunnelFn(
            ShopFunnel.B2B_VM_ON_DEMAND_SUBSCRIPTION,
          ),
          target: NavigationState.SubscribingToVmOnDemand,
        },
        {
          cond: options.guards.isFunnelFn(ShopFunnel.B2B_PURCHASE),
          actions: options.actions.setContextValue(
            'productFamilyId',
            ProductFamilyId.CLOUDPC,
          ),
          target: NavigationState.ViewingBusinessCatalog,
        },
        {
          cond: options.guards.isFunnelFn(ShopFunnel.B2B_UPDATE_STORAGE),
          target: NavigationState.UpdatingBusinessVmStorage,
        },
        {
          cond: options.guards.isFunnelFn(
            ShopFunnel.B2C_DRIVE_FREE_MOBILE_APP_SIGNUP,
          ),
          target: NavigationState.LoggingIn,
        },
        {
          cond: options.guards.isFunnelFn(ShopFunnel.CHANGE_PLAN),
          target: NavigationState.ChangePlan,
        },
        {
          cond: options.guards.isFunnelFn(ShopFunnel.BUNDLE),
          target: NavigationState.ViewingBundleList,
        },
        {
          cond: combineGuards.and(
            options.guards.isFunnelFn(
              ShopFunnel.GAME_STORE_BUY_GAME_AS_SHADOW_SUBSCRIBER,
            ),
            ctx => !ctx.user,
          ),
          actions: options.actions.setContextValue('authFlow', 'login'),
          target: NavigationState.LoggingIn,
        },
        {
          cond: options.guards.isFunnelFn(
            ShopFunnel.GAME_STORE_BUY_GAME_AS_SHADOW_SUBSCRIBER,
          ),
          target: NavigationState.BuyingGameAsShadowSubscriber,
        },
        {
          cond: options.guards.isFunnelFn(
            ShopFunnel.GAME_STORE_BUY_GAME_AS_PROSPECT,
          ),
          target: NavigationState.BuyingGameAsProspect,
        },
        {
          cond: combineGuards.and(
            options.guards.isFunnelFn(ShopFunnel.UPDATE_ALWAYS_ON),
            ctx => !ctx.user,
          ),
          actions: options.actions.setContextValue('authFlow', 'login'),
          target: NavigationState.LoggingIn,
        },
        {
          cond: options.guards.isFunnelFn(ShopFunnel.UPDATE_ALWAYS_ON),
          target: NavigationState.UpdatingAlwaysOnOption,
        },
        {
          cond: combineGuards.and(
            options.guards.isFunnelFn(ShopFunnel.RETENTION_UPGRADE),
            ctx => !ctx.user,
          ),
          actions: options.actions.setContextValue('authFlow', 'login'),
          target: NavigationState.LoggingIn,
        },
        {
          cond: options.guards.isFunnelFn(ShopFunnel.RETENTION_UPGRADE),
          target: NavigationState.UpgradingOfferForRetention,
        },
        { target: NavigationState.ViewingCatalog },
      ],
      'initialState.SET_FLAGS': {
        actions: options.actions.setFeatureFlags,
      },
    },
  },
  {
    trackable: false,
  },
);
