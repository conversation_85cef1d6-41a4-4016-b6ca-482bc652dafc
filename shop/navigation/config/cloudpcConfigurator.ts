import { ProductType } from 'types';
import { choose } from 'xstate';

import { combineGuards, options } from '../options';
import { NavigationState } from '../types';
import {
  ISM_CloudpcConfigurator_ChangeOffer_Event,
  ISM_CloudpcConfigurator_SetDatacenterName_Event,
} from '../types/events/cloudpcConfigurator';
import { createState } from '../utils/createState';

import { ShopFunnel } from '@/types/shop';

export const cloudpcConfigurator =
  createState<NavigationState.CloudpcConfigurator>({
    on: {
      '': [
        // If cart is empty, we should go to the offer list page
        {
          cond: options.guards.isCartEmpty,
          target: NavigationState.ViewingOfferList,
        },
        // If user is not geolocalized, we should go to the geolocalization page
        {
          cond: combineGuards.and(
            options.guards.isFunnelFn(ShopFunnel.B2C_MAIN),
            options.guards.hasContextValueFn('geolocalized', false),
          ),
          target: NavigationState.GeolocalizeUser,
        },
        // If user has no zipcode, we should reset the productId param and go to the offer list page
        {
          cond: combineGuards.and(
            options.guards.isFunnelFn(ShopFunnel.B2C_MAIN),
            combineGuards.not(
              combineGuards.or(
                options.guards.hasContextValueFn('zipcode'),
                options.guards.hasFeatureFlagFn(
                  'feature_fast_funnel_enabled',
                  flagValue => !!flagValue,
                ),
              ),
            ),
          ),
          target: NavigationState.ViewingOfferList,
        },
      ],
      'cloudpcConfigurator.NEXT': [
        {
          actions: [options.actions.setContextValue('authFlow')],
          target: NavigationState.LoggingIn,
        },
      ],
      'cloudpcConfigurator.BACK': [
        {
          cond: options.guards.isFunnelFn(ShopFunnel.CHANGE_PLAN),
          actions: options.actions.emptyCart,
          target: NavigationState.ChangePlan,
        },
        { target: NavigationState.ViewingOfferList },
      ],
      'cloudpcConfigurator.CHANGE_OFFER': {
        actions: [
          options.actions.setRecurringTracking<ISM_CloudpcConfigurator_ChangeOffer_Event>(
            (context, event) => {
              const baseProductId = context.productId;
              const newProductId = event.payload.cart.find(offerInCart => {
                return offerInCart.type === ProductType.PLAN;
              })?.productId;

              if (!baseProductId || !newProductId) {
                return {};
              }

              return {
                change_offer_statement: baseProductId !== newProductId,
                offer_departure: baseProductId,
                offer_switched: newProductId,
              };
            },
          ),
          choose([
            {
              cond: (ctx, event) => {
                const baseProductId = ctx.productId;
                const newProductId = event.payload.cart.find(offerInCart => {
                  return offerInCart.type === ProductType.PLAN;
                })?.productId;

                return baseProductId !== newProductId;
              },
              actions: [
                // options.actions.setContextValue('datacenterName', null),
              ],
            },
          ]),
          options.actions.setCart,
        ],
      },
      'cloudpcConfigurator.SET_ITEM_TO_CART': [
        {
          cond: (_, event) => event.payload.lineItem.quantity > 0,
          actions: options.actions.addItemToCart,
        },
        {
          actions: options.actions.removeItemFromCart,
        },
      ],
      'cloudpcConfigurator.REMOVE_ITEM_FROM_CART': {
        actions: options.actions.removeItemFromCart,
      },
      'cloudpcConfigurator.SET_DATACENTER_NAME': {
        actions: [
          options.actions.setRecurringTracking<ISM_CloudpcConfigurator_SetDatacenterName_Event>(
            (context, event) => {
              const { recommended_datacenter } =
                context.recurringTrackingData ?? {};

              const changingDatacenter =
                !!recommended_datacenter &&
                recommended_datacenter !== event.payload.datacenterName;

              const recommendedDatacenter = changingDatacenter
                ? recommended_datacenter
                : event.payload.datacenterName;

              return {
                recommended_datacenter: recommendedDatacenter,
                datacenter: event.payload.datacenterName,
                datacenter_state: event.payload.datacenterStatus,
                changing_datacenter: changingDatacenter,
              };
            },
          ),
          options.actions.setContextValue('datacenterName'),
        ],
      },
      'cloudpcConfigurator.CANCEL_FUNNEL': {
        actions: options.actions.setMustResetState,
      },
    },
  });
