{"account": {"billing": {"contactSupport": "", "title": "", "update": {"error": {"message": ""}, "submit": {"label": ""}, "success": {"message": ""}}}, "company": {"update": {"success": {"message": "", "title": ""}, "error": {"message": ""}}}, "payment": {"add": {"link": ""}, "edit": {"link": ""}, "type": {"none": "", "card": "", "bancontact": "", "ideal": "", "paypal_express_checkout": "", "sepa": "", "sofort": ""}}, "privacy": {"cta": {"label": ""}, "title": ""}, "user": {"downloadData": {"label": ""}, "editEmail": {"label": ""}, "form": {"submit": {"label": ""}}, "paymentMethod": {"title": ""}, "resendVerificationEmail": {"label": ""}, "security": {"buttons": {"hub": "", "password": ""}, "title": "", "text": ""}, "socials": {"buttons": {"text": ""}, "text": "", "title": ""}, "title": "", "update": {"error": {"message": ""}, "success": {"message": ""}}}}, "company": {"button": "", "title": ""}, "consentBanner": {"title": ""}, "footer": {"cgu": "", "companyName": "", "cookies": "", "legal": "", "privacy": ""}, "header": {"login": {"welcome": {"label": ""}}, "logoAlt": "", "logout": {"label": ""}}, "infoBanner": {"unpaid": {"error": {"content": "", "title": ""}, "infos": {"productName": {"cloudpc": "", "shadow-drive": ""}, "content": "", "contentBusiness": "", "daysBeforeTermination_zero": "", "daysBeforeTermination_one": "", "daysBeforeTermination_other": ""}, "success": {"content": "", "title": ""}}}, "invoices": {"list": {"body": {"link": {"csv": "", "pdf": ""}, "status": {"not_paid": "", "paid": "", "payment_due": "", "pending": "", "posted": "", "voided": ""}, "statusTooltip": {"not_paid": "", "payment_due": "", "pending": "", "posted": "", "voided": "", "notPaid": ""}}, "heading": {"date": "", "status": "", "total": ""}, "download": {"error": ""}}, "noInvoice": "", "title": "", "total": ""}, "subscription": {"details": {"hasStartingPrice": "", "status": {"active": "", "inactive": "", "pending": "", "future": "", "non_renewing": ""}, "scheduledChangesName": {"change_plan": "", "default": "", "reduce_extra_storage": "", "remove_power_upgrade": "", "update_ram": ""}, "scheduledChangesProductFamily": {"shadow-drive": "", "cloudpc": ""}, "name": {"unknown": "", "cloudpc-b2b-edu-plan-a-2022": "", "cloudpc-b2b-edu-plan-b-2022": "", "cloudpc-b2b-edu-plan-c-2022": "", "cloudpc-b2b-edu-plan-d-2022": "", "cloudpc-b2b-nogpu-plan-a-2023": "", "cloudpc-b2b-plan-a-2022": "", "cloudpc-b2b-plan-b-2022": "", "cloudpc-b2b-plan-c-2022": "", "cloudpc-b2b-plan-d-2022": "", "cloudpc-b2b-premium2022": "", "cloudpc-b2b-standard2022": "", "cloudpc-b2c-A4000power-2022": "", "cloudpc-b2c-power2022-c1": "", "cloudpc-b2c-power2022-c12": "", "cloudpc-b2c-power2023": "", "cloudpc-b2c-standard2021-c1": "", "cloudpc-b2c-standard2021-c12": "", "cloudpc-b2c-standard2023": "", "cloudpc-b2p-nogpu-plan-a-2023": "", "cloudpc-b2p-plan-a-2022": "", "cloudpc-b2p-plan-b-2022": "", "cloudpc-b2p-plan-c-2022": "", "cloudpc-b2p-plan-d-2022": "", "cloudpc-b2p-plan-e-2022": "", "cloudpc-old-b2c-boost2019-c1": "", "cloudpc-old-b2c-boost2019-c12": "", "cloudpc-old-b2c-infinite2019-c1": "", "cloudpc-old-b2c-infinite2019-c12": "", "cloudpc-old-b2c-infinite2021-c1": "", "cloudpc-old-b2c-ultra2019-c1": "", "cloudpc-old-b2c-ultra2019-c12": "", "cloudpc-old-b2c-ultra2021-c1": "", "shadow-drive-b2c-free": "", "shadow-drive-b2c-premium": "", "cloudpc-b2c-discovery2024": "", "shadow-drive-b2c-premium_b": "", "cloudpc-b2c-newboost2025": "", "cloudpc-b2c-newboost2025-EUR-Every": "", "cloudpc-b2c-standard2025": "", "cloudpc-b2c-standard2025-OS": "", "cloudpc-b2c-standard2025-summer-EUR-Every": "", "cloudpc-b2c-standard2025-summer": "", "cloudpc-b2p-newstandard2025": "", "cloudpc-b2p-newstandard2025-EUR-Every": ""}, "scheduledChange": {"periodicity": "", "plan": ""}}, "cancelDrive": {"confirmation": {"subscription": {"changedYourMind": "", "item-1": "", "item-2": "", "item-3": ""}, "subtitle": "", "title": ""}, "information": {"subtitle": "", "title": "", "whenSubscriptionActive": {"access": "", "backupTip1": "", "backupTip2": "", "enjoy": "", "title": ""}, "whenSubscriptionEnds": {"dataAccess": "", "description": "", "reSubscribe": {"title": ""}, "shadowAccess": "", "title": ""}}, "notification": {"error": ""}, "reason": {"placeholder": ""}, "survey": {"title": ""}}, "cancellationReasons": {"category": {"financialReason": "", "productAndUsage": "", "technicalIssue": ""}, "reason": {"incompatibilitySoftwareOrGame": "", "itWasJustATest": "", "latencyIssue": "", "noNeedAnymore": "", "personalFinancialIssue": "", "priceTooHigh": "", "shadowSpecTooWeak": "", "stabilityIssue": "", "startIssue": "", "storageNotBigEnough": "", "tooManyConstraints": "", "weakInternetConnection": ""}}, "cancelVm": {"confirmation": {"subscription": {"changedYourMind": "", "item-1": "", "item-2": "", "item-3": ""}, "subtitle": "", "title": ""}, "discount": {"acceptDiscountButton": {"label": "", "upgradeLabel": ""}, "declineDiscountButton": {"label": ""}, "description": "", "notification": {"error": ""}, "title": "", "upgradeDescription": ""}, "discountAccepted": {"confirmationText": "", "confirmationText2": "", "title": ""}, "information": {"subtitle": "", "whenSubscriptionActive": {"access": "", "backupTip1": "", "backupTip2": "", "enjoy": "", "title": ""}, "whenSubscriptionEnds": {"dataAccess": "", "description": "", "reSubscribe": {"title": ""}, "shadowAccess": "", "title": ""}, "title": ""}, "notification": {"error": ""}, "otherReason": {"placeholder": "", "title": ""}, "survey": {"subtitle": "", "title": ""}}, "plan": {"addon": {"drive": {"get": {"link": ""}, "upgrade": {"link": ""}}}, "details": "", "drive": {"action": {"cancel": "", "edit": "", "reactivate": "", "resubscribe": "", "upgrade": ""}, "application": {"link": {"label": "", "title": ""}}, "description": "", "title": ""}, "storage": {"ctaLabel": "", "alert": {"title": "", "description": ""}}, "vm": {"action": {"changePlan": {"alert": {"description": "", "title": ""}, "title": ""}, "delete": "", "reactivate": "", "changeVmPeriodicity": ""}}}, "reactivateVm": {"notification": {"error": "", "success": ""}, "title": ""}, "renameVm": {"title": ""}, "resetVm": {"alert": {"description": "", "title": ""}, "notification": {"error": "", "success": ""}, "resetLabel": "", "selectConfiguration": "", "subtitle": "", "title": ""}, "vmName": {"notification": {"error": "", "success": ""}}, "driveGroups": {"manageGroupMembers": {"status": {"pending": "", "active": "", "disabled": "", "expired": ""}}}, "periodicity": {"adjective": {"day_one": "", "day_other": "", "week_one": "", "week_other": "", "month_one": "", "month_other": "", "year_one": "", "year_other": ""}, "relative": {"day_one": "", "day_other": "", "week_one": "", "week_other": "", "month_one": "", "month_other": "", "year_one": "", "year_other": ""}, "save": ""}, "gamePurchased": {"success": {"content": "", "title": ""}}, "update": {"success": {"content": "", "title": ""}}, "subscribeAlwaysOn": {"title": "", "info": {"description": "", "title": ""}, "alert": {"title": "", "description": ""}}, "unsubscribeAlwaysOn": {"alert": {"description": "", "title": ""}, "title": ""}, "cancelScheduledChange": {"description": "", "notification": {"error": "", "success": ""}, "title": ""}, "promoBanner": {"newBoostOffer": {"button": "", "text": ""}}, "promoAlert": {"newBoostOffer": {"title": "", "button": "", "text": ""}}, "changeVmPeriodicity": {"estimateApiError": "", "success": {"content": ""}, "modal": {"bullet-1": {"title": "", "description": ""}, "bullet-2": {"title": "", "description": ""}, "subtitle": "", "title": ""}, "updateApiError": ""}, "vmDetails": {"vm": {"periodicity": {"label": ""}}}}, "support": {"form": {"placeholder": {"selectTopic": "", "message": "", "selectSystem": "", "selectIssue": "", "selectVm": ""}, "os": {"windows": "", "mac": "", "ios": "", "android": "", "browser": "", "linux": "", "appleTv": "", "androidTv": "", "raspberry": "", "other": ""}, "m3": {"soundIssue": "", "latencyIssue": "", "inputIssue": "", "longLoading": "", "artefact": "", "display": "", "deviceCompatibility": "", "appCrashes": "", "errorMessage": "", "other": ""}, "m1": {"other": "", "accessShadow": "", "shadowAwesome": "", "improveShadow": "", "shadowPCIssue": "", "shadowDriveIssue": "", "subscriptionQuestion": ""}, "m2": {"billRegul": "", "billQuestion": "", "promo": "", "command": "", "cancel": "", "deleteAccount": "", "2FA": "", "personalInfo": "", "other": "", "emailHacked": ""}, "description": "", "title": "", "notification": {"error": "", "success": "", "uploading": ""}, "attachments": {"dragText": "", "hint": "", "sizeError": "", "totalSizeError": ""}}, "ressources": {"description": "", "faq": {"description": "", "title": ""}, "helpcenter": {"description": "", "title": ""}, "title": "", "userId": ""}, "unknownEmail": "", "title": ""}, "emailing": {"shadow": {"title": "", "description": ""}, "storage": {"title": "", "description": ""}, "drive": {"title": "", "description": ""}}, "billingDetails": {"title": ""}, "notFound": {"description": "", "title": ""}, "paymentMethod": {"title": ""}, "userManager": {"title": "", "notAdminError": "", "inviteError": {"description": "", "goToManager": "", "title": ""}, "inviteSuccess": {"description": "", "title": ""}}, "vmManager": {"title": ""}, "navigation": {"account": {"personal": "", "security": "", "title": ""}, "billing": {"details": "", "invoices": "", "paymentMethod": "", "title": ""}, "download": "", "gameStore": "", "shadowDrive": "", "support": "", "user": "", "vms": "", "shadowPC": {"title": "", "users": "", "vms": ""}}, "meta": {"account": {"personal": {"title": ""}, "security": {"title": ""}}, "billing": {"details": {"title": ""}, "invoices": {"title": ""}, "paymentMethod": {"title": ""}}, "default": {"description": "", "title": ""}, "support": {"title": ""}, "vm": {"manager": {"title": ""}, "user": {"title": ""}}}, "downloadData": {"modal": {"title": ""}}, "api": {"error": {"description": "", "title": ""}}, "form": {"address1": {"error": {"required": ""}, "label": ""}, "birthdate": {"error": {"required": "", "invalid": ""}, "label": ""}, "city": {"error": {"required": ""}, "label": ""}, "companyName": {"label": ""}, "country": {"error": {"required": ""}, "label": ""}, "email": {"error": {"email": "", "required": ""}}, "firstname": {"error": {"required": ""}, "label": ""}, "firstName": {"error": {"required": ""}, "label": ""}, "language": {"label": ""}, "lastname": {"error": {"required": ""}, "label": ""}, "lastName": {"error": {"required": ""}, "label": ""}, "phone": {"label": "", "error": {"required": ""}}, "vatNumber": {"error": {"invalid": ""}, "label": ""}, "vmName": {"error": {"maxLength": "", "minLength": "", "specialChar": "", "required": ""}, "label": ""}, "word": {"info": "", "error": {"mismatch": ""}}, "zipcode": {"error": {"required": ""}, "label": ""}, "select": {"automatic": {"label": ""}, "manual": {"label": ""}, "error": {"default": ""}}, "configuration": {"label": "", "information": ""}, "keyboard": {"label": ""}, "default": {"error": {"maxLength": "", "required": ""}}, "periodicity": {"placeholder": "", "label": ""}}, "global": {"cancel": "", "confirm": "", "continue": "", "email": "", "invite": "", "loading": "", "next": "", "noResults": "", "ok": "", "revoke": "", "send": "", "unknown": "", "skipReason": "", "contactSupport": "", "moreInfo": "", "new": ""}, "login": {"error": {"description": "", "title": ""}}, "list": {"user": {"activateMember": {"notification": {"error": "", "success": ""}, "label": ""}, "deleteMember": {"modal": {"content": "", "title": ""}, "notification": {"alreadyAssigned": "", "error": "", "success": ""}, "label": ""}, "resendInvitation": {"notification": {"error": "", "success": ""}}, "invite": {"error": {"message": "", "title": ""}, "success": {"title": "", "message": ""}}, "updateUserRole": {"notification": {"success": "", "error": ""}, "demote": {"label": ""}, "promote": {"label": ""}}, "status": {"pending": "", "expired": "", "disabled": ""}, "role": {"owner": "", "member": "", "admin": "", "manager": ""}, "total": "", "showConfiguration": "", "table": {"action": ""}, "searchPlaceholder": ""}, "vm": {"noVmFound": "", "createFirstVmButtonLabel": "", "options": {"addVmButtonLabel": ""}, "vmStatus": {"maintenance": "", "on_hold": "", "running": "", "stopped": ""}, "subscriptionStatus": {"active": "", "cancelled": "", "future": "", "in_trial": "", "non_renewing": "", "not_paid": "", "paused": ""}, "internalStorage": {"label": "", "value": ""}, "user": {"title": ""}, "alwaysOn": {"tooltip": "", "label": ""}, "created": {"notification": {"success": {"title": "", "message": ""}}}, "resetVm": "", "stopVm": {"notification": {"error": "", "success": ""}, "label": ""}, "renameVm": "", "deleteVm": "", "reset": "", "assignOrRevokeMember": {"revoke": {"modal": {"content": "", "title": ""}, "notification": {"error": "", "success": ""}, "label": ""}, "assign": {"placeholder": "", "noUser": "", "label": "", "notification": {"error": "", "success": ""}}, "vmCreating": "", "error": {"vmNotStopped": "", "userAlreadyAssigned": ""}, "title": ""}, "creationDate": "", "subscriptionRenewal": "", "datacenter": "", "offer": "", "subscriptionEnd": "", "status": "", "clone": {"noStock": "", "notCloneable": "", "disabled": "", "enabled": "", "notStopped": ""}, "total": "", "showConfiguration": "", "table": {"action": ""}, "noSearchResults": "", "searchPlaceholder": ""}, "heading": {"role": "", "created": "", "user": "", "name": "", "tags": "", "datacenter": "", "status": ""}}, "tagManager": {"notification": {"success": "", "error": "", "errorLength": ""}, "noTags": "", "placeholder": "", "title": ""}}