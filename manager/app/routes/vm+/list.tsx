import { json, LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { useSearchParams } from '@remix-run/react';
import { Flex, Grid, Space, Typography } from 'antd';
import { useDatacenterList, useDebounce } from 'hooks';
import { usePaginatedSubscriptions } from 'hooks';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Locale, ProductFamilyId, ProductType, UserRole } from 'types';
import { SortableColumns } from 'types';

import VmManagerRedirectAlert from '@/app/components/vmManager/VmManagerRedirectAlert';
import VmPromoAlert from '@/app/components/vmManager/VmPromoAlert';
import { VmSearch } from '@/app/components/vmManager/VmSearch/VmSearch';
import CreateVmButton from '@/app/components/vmManager/VmTable/CreateVmButton';
import VmTable from '@/app/components/vmManager/VmTable/VmTable';
import {
  useCurrentMember,
  useManyMembers,
} from '@/app/hooks/reactQuery/member/useMember';
import { useManyVmStatus } from '@/app/hooks/reactQuery/vm/useVdi';
import usePagination from '@/app/hooks/usePagination';
import useSorter from '@/app/hooks/useSorter';
import i18nServer from '@/app/modules/i18n.server';
import { IVmManager } from '@/app/types/adminManagers';
import { computeMeta } from '@/app/utils/meta';
import { getVmRules } from '@/app/utils/rules';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const locale = (await i18nServer.getLocale(request)) as Locale;
  const t = await i18nServer.getFixedT(locale);
  const title = t('meta.vm.manager.title', 'Shadow - My Pcs');
  const description = t(
    'meta.default.description',
    'Manage your Shadow account profile, password, security options, product subscriptions, users and payment method',
  );
  return json({ description, locale, title });
};

export const meta: MetaFunction<typeof loader> = ({ data }) => [
  ...computeMeta(data),
];

const VmManager = () => {
  const { t } = useTranslation();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  const currentMemberQuery = useCurrentMember();
  const currentMemberRole = currentMemberQuery.data?.role ?? UserRole.MEMBER;

  const vmRules = getVmRules();

  const { isLoading: isDatacenterListLoading } = useDatacenterList();
  const { currentPage, setCurrentPage, pageSize, setPageSize } =
    usePagination();

  const [searchParams] = useSearchParams();

  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 1000);

  //If param vm_created, so we order by default by creation date desc
  const sortFromParam =
    searchParams.get('vm_created') !== null
      ? SortableColumns.CREATED_AT
      : undefined;

  const { orderBy, setOrderBy, sortBy, setSortBy } = useSorter(sortFromParam);
  const { data: subscriptionsData, isLoading: isSubscriptionsLoading } =
    usePaginatedSubscriptions(
      currentMemberRole === UserRole.MEMBER,
      currentPage - 1,
      pageSize,
      [],
      ProductFamilyId.CLOUDPC,
      orderBy,
      sortBy,
      debouncedSearchQuery,
    );

  const { data: statuses } = useManyVmStatus(subscriptionsData?.items || []);

  const { data: members } = useManyMembers(
    subscriptionsData?.items.map(sub => sub.user_id) || [],
  );

  const vmData: IVmManager[] =
    subscriptionsData?.items.map((sub, index) => {
      const foundPlan = sub.items.find(
        ({ item_type }) => item_type === ProductType.PLAN,
      );
      const fetchedUser = members?.find(
        member => member?.user?.id === sub.user_id,
      );
      const fetchedVmStatus = statuses?.[index];

      return {
        id: sub.id,
        createdAt: sub.created_at,
        subscriptionStatus: sub.status,
        isVmRunning: fetchedVmStatus?.running || false,
        isVmInMaintenanceMode: fetchedVmStatus?.maintenance || false,
        isVmOnHold: sub.on_hold || false,
        datacenter:
          fetchedVmStatus?.datacenter || sub.meta_data?.datacenter || undefined,
        type: foundPlan?.name || '',
        user: fetchedUser?.user || undefined,
        name: sub.name,
        subscription: sub,
        tags: sub.tags,
      };
    }) || [];

  return (
    <>
      <Typography.Title level={1}>
        {t('vmManager.title', 'Manage my Vms')}
      </Typography.Title>

      <VmPromoAlert vmsData={vmData} vmRules={vmRules[currentMemberRole]} />
      <VmManagerRedirectAlert />

      <Space size="middle" direction="vertical">
        <Flex
          justify="space-between"
          gap={screens.md ? 'large' : 'middle'}
          vertical={!screens.md}
        >
          {/* Vm create */}
          {vmRules[currentMemberRole].canCreateVm && (
            <CreateVmButton
              isLoading={isDatacenterListLoading || isSubscriptionsLoading}
            />
          )}
          {/* Vm search */}
          {vmRules[currentMemberRole].canSearchVm && (
            <VmSearch
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              isLoading={isSubscriptionsLoading}
            />
          )}
        </Flex>
        <VmTable
          currentPage={currentPage}
          data={vmData}
          isLoading={isDatacenterListLoading || isSubscriptionsLoading}
          vmRules={vmRules[currentMemberRole]}
          orderBy={orderBy}
          pageSize={pageSize}
          setCurrentPage={setCurrentPage}
          setOrderBy={setOrderBy}
          setPageSize={setPageSize}
          setSortBy={setSortBy}
          sortBy={sortBy}
          totalItems={subscriptionsData?.total_items}
          searchQuery={debouncedSearchQuery}
        />
      </Space>
    </>
  );
};

export default VmManager;
