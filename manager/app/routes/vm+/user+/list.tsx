import { json, LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { Alert, Flex, Grid, Space, Typography } from 'antd';
import { useDebounce } from 'hooks';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Locale, UserRole } from 'types';

import UserManagerOptions from '@/app/components/userManager/UserManagerOption';
import { UserSearch } from '@/app/components/userManager/UserSearch/UserSearch';
import { UserTable } from '@/app/components/userManager/UserTable';
import {
  useCurrentMember,
  useMembers,
} from '@/app/hooks/reactQuery/member/useMember';
import i18nServer from '@/app/modules/i18n.server';
import { IUserManager } from '@/app/types/adminManagers';
import { computeMeta } from '@/app/utils/meta';
import { getNavigationRules, getRules } from '@/app/utils/rules';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const locale = (await i18nServer.getLocale(request)) as Locale;
  const t = await i18nServer.getFixedT(locale);
  const title = t('meta.vm.user.title', 'Shadow - Users');
  const description = t(
    'meta.default.description',
    'Manage your Shadow account profile, password, security options, product subscriptions, users and payment method',
  );
  return json({ description, locale, title });
};

export const meta: MetaFunction<typeof loader> = ({ data }) => [
  ...computeMeta(data),
];

const UserManager = () => {
  const { t } = useTranslation();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  const useMembersQuery = useMembers(undefined);
  const currentMemberQuery = useCurrentMember();
  const currentMemberRole = currentMemberQuery.data?.role ?? UserRole.MEMBER;

  const navigationRules = getNavigationRules();
  const rules = getRules(currentMemberRole);

  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 1000);

  const [userRows, setUserRows] = useState<IUserManager[]>([]);

  useEffect(() => {
    if (
      useMembersQuery.isSuccess &&
      useMembersQuery.data &&
      Array.isArray(useMembersQuery.data)
    ) {
      const mappedUsers = useMembersQuery.data.map(
        ({
          status,
          role,
          user,
          invite_email,
          invite_created_on,
          assigned_vm_product_families,
        }) =>
          ({
            status,
            role,
            id: user?.id || invite_email,
            email: user?.email || invite_email,
            first_name: user?.first_name || '',
            last_name: user?.last_name || '',
            createdAt: invite_created_on || user?.created_at,
            tags: user?.tags || [],
            assigned_vm_product_families: assigned_vm_product_families,
            isAssigned: true,
          } as IUserManager),
      );

      setUserRows(mappedUsers);
    }
  }, [useMembersQuery.data, useMembersQuery.isSuccess]);

  // Filter users based on search query
  const filteredUserRows = userRows.filter(user => {
    if (!debouncedSearchQuery) {
      return true;
    }

    const searchLower = debouncedSearchQuery.toLowerCase();
    const fullName = `${user.first_name} ${user.last_name}`.toLowerCase();
    const email = user.email.toLowerCase();
    const tags = user.tags.map((tag: unknown) => {
      if (typeof tag === 'string') {
        return tag.toLowerCase();
      } else if (tag && typeof tag === 'object' && 'name' in tag) {
        return (tag as { name: string }).name.toLowerCase();
      } else {
        return String(tag).toLowerCase();
      }
    });

    return (
      fullName.includes(searchLower) ||
      email.includes(searchLower) ||
      tags.some(tag => tag.includes(searchLower))
    );
  });

  if (!navigationRules[currentMemberRole]?.useNavigation.shadowPCUsers) {
    return (
      <Alert
        message={t(
          'userManager.notAdminError',
          "You don't have permission to access to this page",
        )}
        type="warning"
        showIcon
      />
    );
  }

  return (
    <>
      <Typography.Title level={1}>
        {t('userManager.title', 'Manage my Users')}
      </Typography.Title>

      <Space size="middle" direction="vertical">
        <Flex
          justify="space-between"
          gap={screens.md ? 'large' : 'middle'}
          vertical={!screens.md}
        >
          {/* User invite */}
          {rules[currentMemberRole].canInviteUsers && <UserManagerOptions />}
          {/* User search */}
          {rules[currentMemberRole].canSearchUsers && (
            <UserSearch
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              isLoading={useMembersQuery.isLoading}
            />
          )}
        </Flex>
        <UserTable
          data={filteredUserRows}
          isLoading={useMembersQuery.isLoading}
          userDrawerRules={navigationRules[currentMemberRole]}
        />
      </Space>
    </>
  );
};

export default UserManager;
