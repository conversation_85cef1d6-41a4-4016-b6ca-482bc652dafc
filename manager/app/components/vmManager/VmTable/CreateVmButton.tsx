import { PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { IMemberDetails, ProductFamilyId } from 'types';

import Button from '@/app/components/ui/Button/Button';
import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import { SHOP_URL } from '@/app/utils/constants';

interface ICreateVmButton {
  hasNoVm?: boolean;
  isLoading?: boolean;
}

const CreateVmButton = ({
  hasNoVm = false,
  isLoading = false,
}: ICreateVmButton) => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const getNewSubscriptionUrl = isB2b
    ? `${SHOP_URL}?funnel=b2b_purchase`
    : `${SHOP_URL}b2c?familyId=${ProductFamilyId.CLOUDPC}`;

  return (
    <Button
      data-testid={hasNoVm ? 'create_first_vm-button' : 'create_vm-button'}
      href={getNewSubscriptionUrl}
      icon={<PlusOutlined />}
      loading={isLoading}
      noWrap
    >
      {t('list.vm.options.addVmButtonLabel', 'New Shadow PC')}
    </Button>
  );
};

export default CreateVmButton;
