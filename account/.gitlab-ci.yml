.account_build_template:
  stage: build
  image:
    name: gcr.io/kaniko-project/executor:v1.9.1-debug
    entrypoint: ['']
  script:
    - mkdir -p /kaniko/dot_envs
    - cat "$K8S_ACCOUNT_ENV_FILE" > "$CI_PROJECT_DIR/account/.env.local"
    - echo -e "\nNEXT_PUBLIC_FEATURE_MESSAGE=${FEATURE_MESSAGE}" >> "$CI_PROJECT_DIR/account/.env.local"
    - >-
      apt update && apt install jq
      echo $(jq -cn \
        --arg<PERSON>son docker "$DOCKER_AUTH_CONFIG" \
        --arg gitlab_auth "$(echo -n "${CI_REGISTRY_USER}:${CI_REGISTRY_PASSWORD}" | base64)" \
        '$docker.auths * {"registry.gitlab.com": {"auth": $gitlab_auth}} | {auths: .}') > /kaniko/.docker/config.json
    - >-
      /kaniko/executor --context ${CI_PROJECT_DIR} --dockerfile ${CI_PROJECT_DIR}/account/Dockerfile
      --destination ${CI_REGISTRY_IMAGE_PATH}:${DEPLOYMENT_IMAGE_TAG}
      --build-arg PROJECT_ID=${CI_PROJECT_ID}
      --build-arg TOKEN=${DESIGN_TOKENS_API_TOKEN}
      --build-arg USERNAME=gitlab-ci-token
    - echo "DOCKER_IMAGE=${CI_REGISTRY_IMAGE_PATH}" >> build-account.env
    - echo "DOCKER_TAG=${DEPLOYMENT_IMAGE_TAG}" >> build-account.env
  tags:
    - blade
    - onsite

account_test:
  stage: prebuild
  image:
    name: node:20-alpine3.18
    entrypoint: ['']
  script:
    - apk update && apk add --no-cache libc6-compat python3 make g++
    - npm config set //gitlab.com/api/v4/projects/********/packages/npm/:_authToken=${DESIGN_TOKENS_API_TOKEN}
    - yarn install --frozen-lockfile --non-interactive
    - yarn workspace account test
  tags:
    - blade
    - onsite
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" || $CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_TAG =~ /^v?[0-9]+[.][0-9]+([.][0-9]+)?(-preprod)?$/
      changes:
        - account/**/*
        - shop/**/*
        - manager/**/*
        - common-components/**/*
        - shared-components/**/*
        - utils/**/*

account_build_preview:
  extends: .account_build_template
  variables:
    K8S_ACCOUNT_ENV_FILE: $K8S_DEV_ACCOUNT_ENV
  before_script:
    - export CI_REGISTRY_IMAGE_PATH="${CI_REGISTRY_IMAGE}/account"
    - export DEPLOYMENT_IMAGE_TAG=`echo ${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME} | tr '/' '-'`
    - >
      export FEATURE_MESSAGE="{ \"feature\": \"${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME}\", \"commit\": \"${CI_COMMIT_SHORT_SHA}\", \"build\": \"${CI_PIPELINE_ID}\" }"
  needs:
    - job: account_test
    - job: build_preview
  tags:
    - blade
    - onsite
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - account/**/*
        - shop/**/*
        - common-components/**/*
        - shared-components/**/*
        - utils/**/*

account_build_dev:
  extends: .account_build_template
  variables:
    K8S_ACCOUNT_ENV_FILE: $K8S_DEV_ACCOUNT_ENV
  before_script:
    - export CI_REGISTRY_IMAGE_PATH="${CI_REGISTRY_IMAGE}/account"
    - export DEPLOYMENT_IMAGE_TAG=latest
  needs:
    - job: account_test
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"
      changes:
        - account/**/*
        - common-components/**/*
        - shared-components/**/*
        - utils/**/*

account_build_preprod:
  extends: .account_build_template
  variables:
    K8S_ACCOUNT_ENV_FILE: $K8S_PREPROD_ACCOUNT_ENV
  before_script:
    - export CI_REGISTRY_IMAGE_PATH="${CI_REGISTRY_IMAGE}/account/preprod"
    - export DEPLOYMENT_IMAGE_TAG=${CI_COMMIT_TAG}
  needs:
    - job: account_test
  artifacts:
    reports:
      dotenv: build-account.env
  rules:
    - if: $CI_COMMIT_TAG =~ /^v?[0-9]+[.][0-9]+([.][0-9]+)?-preprod$/

account_deploy_preprod:
  extends: .gitops_deploy
  script:
    - >-
      cd cbp-frontend/overlays/preprod/ ;
      kustomize edit set image account-img=${DOCKER_IMAGE}:${DOCKER_TAG} ;
      git commit -m "set account image tag to ${DOCKER_TAG} in bases" kustomization.yml ;
      git push origin main
  needs:
    - job: account_build_preprod
      artifacts: true
  rules:
    - if: $CI_COMMIT_TAG =~ /^v?[0-9]+[.][0-9]+([.][0-9]+)?-preprod$/

account_build_prod:
  extends: .account_build_template
  variables:
    K8S_ACCOUNT_ENV_FILE: $K8S_PROD_ACCOUNT_ENV
  before_script:
    - export CI_REGISTRY_IMAGE_PATH="${CI_REGISTRY_IMAGE}/account/prod"
    - export DEPLOYMENT_IMAGE_TAG=${CI_COMMIT_TAG}
  needs:
    - job: account_test
  rules:
    - if: $CI_COMMIT_TAG =~ /^v?[0-9]+[.][0-9]+([.][0-9]+)?$/
